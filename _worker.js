import { connect } from 'cloudflare:sockets';

const GLOBAL_ERROR_HANDLER = {
    errors: [],
    maxErrors: 100,

    log(error, context = 'Unknown', stack = null) {
        const errorInfo = {
            timestamp: new Date().toISOString(),
            context,
            message: error?.message || String(error),
            stack: stack || error?.stack || 'No stack trace',
            type: error?.constructor?.name || 'Unknown',
            errorId: Math.random().toString(36).substr(2, 9)
        };

        this.errors.push(errorInfo);
        if (this.errors.length > this.maxErrors) {
            this.errors.shift();
        }

        console.error(`[ERROR HANDLER] ${context} [ID:${errorInfo.errorId}]:`, errorInfo);
        return errorInfo;
    },

    getRecentErrors() {
        return this.errors.slice(-10);
    },

    clear() {
        this.errors = [];
        console.log('[GLOBAL_ERROR_HANDLER] Error history cleared');
    }
};

const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip',
    targetProtocolType0: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d8-_-1b0-_-194-_-1cc-_-1cc',
    targetProtocolType1: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8',
    targetPathType0: 'vlws',
    targetPathType1: 'trws',
};

const globalSessionConfig = {
    connect: {
        connectMode: 'direct', // Optional direct relayip relaysocks Default direct
        retryMode: 'relayip', // Optional relayip relaysocks Default relayip
    },

    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43',
        pass: 'a233255z',
        sha224: '419023a775279d21cdbda41971c0bb52e962f11b4f4bfba6015a268b',
    },

    relay: {
        ip: 'jp.almain126.changeip.biz',
        _port: null,
        get port() {
            if (this._port !== null) {
                return this._port;
            }
            return this.ip.includes(':') ? this.ip.split(':')[1] : (null || undefined);
        },
        set port(value) {
            this._port = value;
        },
        // Example:  user:pass@host:port  or  host:port
        socks: 'web5.serv00.com:13668',
        // socks: 'user:<EMAIL>:13668',
    },

    api: {
        addresses: 'https://rtmainalraw.pages.dev/az/index.txt', // https://raw.almain126.changeip.org/az/index.txt
        addresses2: 'https://rtmainalraw.pages.dev/az/main.txt',
        directTemplate: 'https://rtmainalraw.pages.dev/az/templatedirect.txt',
        globalTemplate: 'https://rtmainalraw.pages.dev/az/templateglobal.txt',
    },

    misc: {
        subName: 'myMain',
    }
};

const WS_STATES = {
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
};

export default {
    async fetch(request, env, ctx) {
        try {
            const { CONNECT_MODE, RETRY_MODE, USER_GUID, USER_PASS, USER_SHA224, RELAY_IP, RELAY_SOCKS, API_TXT, API_TXT_2, API_DIRECT_TEMPLATE_URL, API_GLOBAL_TEMPLATE_URL } = env;
            globalControllerConfig.connectMode = (CONNECT_MODE || globalSessionConfig.connect.connectMode).toLowerCase();
            globalControllerConfig.retryMode = (RETRY_MODE || globalSessionConfig.connect.retryMode).toLowerCase();

            globalSessionConfig.user.id = USER_GUID || globalSessionConfig.user.id;
            globalSessionConfig.user.pass = USER_PASS || globalSessionConfig.user.pass;
            globalSessionConfig.user.sha224 = USER_SHA224 || globalSessionConfig.user.sha224;
            globalSessionConfig.relay.ip = RELAY_IP || globalSessionConfig.relay.ip;
            globalSessionConfig.relay.socks = RELAY_SOCKS || globalSessionConfig.relay.socks;
            globalSessionConfig.api.addresses = API_TXT || globalSessionConfig.api.addresses; // API_ADDRESSES_URL
            globalSessionConfig.api.addresses2 = API_TXT_2 || globalSessionConfig.api.addresses2; // API_ADDRESSES_URL_2
            globalSessionConfig.api.directTemplate = API_DIRECT_TEMPLATE_URL || globalSessionConfig.api.directTemplate;
            globalSessionConfig.api.globalTemplate = API_GLOBAL_TEMPLATE_URL || globalSessionConfig.api.globalTemplate;

            const userAgent = (request.headers.get('User-Agent') || 'null').toLowerCase();
            const url = new URL(request.url);
            const upgradeHeader = request.headers.get('Upgrade');
            if (!upgradeHeader || upgradeHeader !== 'websocket') {
                switch (url.pathname) {
                    case '/':
                        // return new Response(JSON.stringify(request.cf), { status: 200 });
                        // return new Response(JSON.stringify(request.cf, null, 2), { status: 200 });
                        // return new Response(JSON.stringify(request.cf, null, 4), { status: 200 });
                        return new Response(null, { status: 204 });
                    case `/a`: {
                        return new Response(null, { status: 204 });
                    }
                    case `/z`: {
                        const newResponse = new Response(null, { status: 204 });
                        console.log('Status:', newResponse.status);
                        return newResponse;
                    }
                    case '/debug-errors': {
                        const currentTime = new Date().toISOString();
                        const debugInfo = {
                            allErrors: [...GLOBAL_ERROR_HANDLER.errors],
                            totalErrors: GLOBAL_ERROR_HANDLER.errors.length,
                            timestamp: currentTime,
                            lastErrorTime: GLOBAL_ERROR_HANDLER.errors.length > 0 ?
                                GLOBAL_ERROR_HANDLER.errors[GLOBAL_ERROR_HANDLER.errors.length - 1].timestamp : 'No errors',
                            recentErrors: GLOBAL_ERROR_HANDLER.getRecentErrors(),
                            errorStats: {
                                handleSessionErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.context === 'HandleSession'
                                ).length,
                                connectionErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.message?.includes('Connection failed') || e.message?.includes('dial')
                                ).length,
                                protocolErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.message?.includes('Protocol') || e.message?.includes('Header')
                                ).length,
                                webSocketErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.context?.includes('WebSocket')
                                ).length
                            },
                            config: {
                                connectMode: globalControllerConfig.connectMode,
                                retryMode: globalControllerConfig.retryMode,
                                relayIp: globalSessionConfig.relay.ip,
                                relayPort: globalSessionConfig.relay.port || 443,
                                relaySocks: globalSessionConfig.relay.socks
                            },
                            environment: {
                                url: request.url,
                                method: request.method,
                                userAgent: request.headers.get('User-Agent'),
                                upgradeHeader: request.headers.get('Upgrade'),
                                pathname: url.pathname,
                                headers: Object.fromEntries(request.headers.entries()),
                                cf: request.cf || {}
                            }
                        };

                        return new Response(JSON.stringify(debugInfo, null, 2), {
                            status: 200,
                            headers: {
                                'Content-Type': 'application/json',
                                'Cache-Control': 'no-cache, no-store, must-revalidate',
                                'Pragma': 'no-cache',
                                'Expires': '0'
                            }
                        });
                    }
                    case `/aazz`: {
                        try {
                            const inputString = await fetchRemoteData(globalSessionConfig.api.addresses);
                            // const inputTemplate = await fetchRemoteData(globalSessionConfig.api.directTemplate);
                            const inputTemplate = await fetchRemoteData(globalSessionConfig.api.globalTemplate);

                            const getConfig = (type, tls) =>
                                url.searchParams.toString().includes('b64') || url.searchParams.toString().includes('base64')
                                    ? (() => {
                                        const configs = generateConfigs(inputString, globalSessionConfig.user.id, globalSessionConfig.user.pass, tls, request.headers.get('Host'));
                                        if (type === 'both') return btoa(configs.protocolP0 + '\n' + configs.protocolP1);
                                        if (type === 'p0') return btoa(configs.protocolP0);
                                        if (type === 'p1') return btoa(configs.protocolP1);
                                    })()
                                    : getCustomConfig(inputString, globalSessionConfig.user.id, globalSessionConfig.user.pass, tls, request.headers.get('Host'), type, inputTemplate);

                            const configs = {
                                both: getConfig('both', true),
                                p0: getConfig('p0', true),
                                p1: getConfig('p1', true),
                                bothNotls: getConfig('both', false),
                                p0Notls: getConfig('p0', false),
                                p1Notls: getConfig('p1', false)
                            };

                            const configMappings = [
                                { param: 'both', config: configs.both },
                                { param: 'az', config: configs.p0 },
                                { param: 'za', config: configs.p1 },
                                { param: 'both-notls', config: configs.bothNotls },
                                { param: 'az-notls', config: configs.p0Notls },
                                { param: 'za-notls', config: configs.p1Notls }
                            ];

                            const getResponseConfig = (isMozilla) => ({
                                status: 200,
                                headers: {
                                    "Content-Type": "text/plain;charset=utf-8",
                                    ...(isMozilla ? {} : {
                                        "Content-Disposition": `attachment; filename=${globalSessionConfig.misc.subName}; filename*=utf-8''${encodeURIComponent(globalSessionConfig.misc.subName)}`,
                                        "Profile-Update-Interval": "6"
                                    })
                                }
                            });

                            const isMozilla = userAgent && userAgent.includes('mozilla');
                            const prefixes = ['b64', 'base64'];

                            for (const prefix of prefixes) {
                                for (const { param, config } of configMappings) {
                                    const fullParam = `${prefix}${param}`;
                                    if (url.searchParams.has(fullParam)) {
                                        return new Response(config, getResponseConfig(isMozilla));
                                    }
                                }
                            }

                            for (const { param, config } of configMappings) {
                                if (url.searchParams.has(param)) {
                                    return new Response(config, getResponseConfig(isMozilla));
                                }
                            }

                            // Default case
                            // return new Response(configMappings[0].config, getResponseConfig(isMozilla));
                            // Default case
                            const isB64Request = url.searchParams.toString().includes('b64') || url.searchParams.toString().includes('base64');
                            const defaultConfig = isB64Request
                                ? getConfig('both', true)
                                : configMappings[0].config;

                            return new Response(defaultConfig, getResponseConfig(isMozilla));

                        } catch (error) {
                            return new Response(error.message, { status: 400 });
                        }
                    }

                    default:
                        return new Response('Not found', { status: 404 });
                    // return new Response('Expected Upgrade: websocket', { status: 426 });
                }
                // } else {
            } else if (upgradeHeader === 'websocket') {
                if (url.searchParams.has('relayip')) {
                    globalSessionConfig.relay.ip = url.searchParams.get('relayip') || globalSessionConfig.relay.ip.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/relayip=')) {
                    globalSessionConfig.relay.ip = url.pathname.split('/relayip=')[1]?.trim() || globalSessionConfig.relay.ip.trim();
                    // globalSessionConfig.relay.ip = url.pathname.split('/relayip=')[1].split('/')[0]?.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.searchParams.has('socks')) {
                    globalSessionConfig.relay.socks = url.searchParams.get('socks') || globalSessionConfig.relay.socks.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/socks=')) {
                    globalSessionConfig.relay.socks = url.pathname.split('/socks=')[1]?.trim() || globalSessionConfig.relay.socks.trim();
                    // globalSessionConfig.relay.socks = url.pathname.split('/socks=/i')[1].split('/')[0]?.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                }

                const [relayIp, relayPort] = globalSessionConfig.relay.ip.split(':');
                globalSessionConfig.relay.ip = relayIp;
                if (relayPort) globalSessionConfig.relay.port = relayPort;

                // const handleSessionA = (request, env, ctx) => handleSession(request, env, ctx, globalControllerConfig.targetProtocolType0);
                // const handleSessionZ = (request, env, ctx) => handleSession(request, env, ctx, globalControllerConfig.targetProtocolType1);

                // const handleSessionA = (request, env, ctx) => processSession(request, env, ctx, globalControllerConfig.targetProtocolType0);
                // const handleSessionZ = (request, env, ctx) => processSession(request, env, ctx, globalControllerConfig.targetProtocolType1);

                // const handleSessionA = (request, env, ctx) => handleSession(request, env, ctx, globalControllerConfig.targetProtocolType0);
                // const handleSessionZ = (request, env, ctx) => processSession(request, env, ctx, globalControllerConfig.targetProtocolType1);

                const HANDLER_CHOICE = 1;
                const handlerConfigs = {
                    1: { sessionA: handleSession, sessionZ: handleSession },
                };
                const config = handlerConfigs[HANDLER_CHOICE];
                const handleSessionA = (request, env, ctx) => config.sessionA(request, env, ctx, globalControllerConfig.targetProtocolType0);
                const handleSessionZ = (request, env, ctx) => config.sessionZ(request, env, ctx, globalControllerConfig.targetProtocolType1);

                // const pathHandlers = {
                //     [globalControllerConfig.targetPathType1]: handleSessionZ,
                //     [globalControllerConfig.targetPathType0]: handleSessionA
                // }
                // const pathType = url.pathname.split('/')[1];
                // const handler = pathHandlers[pathType] || handleSessionA
                // // return handler(request, env, ctx)
                // return await handler(request, env, ctx)
                // // return (pathHandlers[pathType] ? pathHandlers[pathType] : handleSessionA)(request, env, ctx)
                // // return await (pathHandlers[pathType] ? pathHandlers[pathType] : handleSessionA)(request, env, ctx)

                let handler;
                const pathType = url.pathname.split('/')[1];
                switch (pathType) {
                    case globalControllerConfig.targetPathType1:
                        handler = handleSessionZ;
                        break;
                    case globalControllerConfig.targetPathType0:
                        handler = handleSessionA;
                        break;
                    default:
                        handler = handleSessionA;
                }

                // return handler(request, env, ctx)
                return await handler(request, env, ctx)
            }
        } catch (error) {
            return new Response(`fetch Error: ${error.message}`, { status: 500 });
            // return new Response(error.toString());
            // return new Response(error.toString(), { status: 400 });
            // return new Response(error.message, { status: 400 });
            // return new Response(error.stack || error.message, { status: 400 });
        }
    },
};

export async function handleSession(request, env, ctx, protocolMode) {
    try {
        // const [client, server] = Object.values(new WebSocketPair());
        const { 0: client, 1: server } = Object.values(new WebSocketPair());
        server.accept();

        const earlyHeader = request.headers.get("sec-websocket-protocol") || "";

        /* ingress modes: transform | readable */
        let ingressMode = "transform";   // ← Change here to switch modes

        let upstreamReadable;
        let tcpInterface = null;
        let tcpReader = null;
        let tcpWriter = null;

        if (ingressMode === "transform") {
            /* ----- TransformStream with IIFE implementation ----- */
            upstreamReadable = ((transformStream) => {
                const holdWriter = transformStream.writable.getWriter();

                if (earlyHeader) {
                    // try {
                    //     holdWriter.write(decodeBase64Url(earlyHeader)).catch(() => { });
                    // } catch (e) { }
                    try {
                        const earlyData = decodeBase64Url(earlyHeader);
                        if (earlyData) {
                            holdWriter.write(earlyData).catch(() => { });
                        }
                    } catch (e) { }
                }

                const handleMessage = (e) => {
                    try {
                        if (holdWriter) {
                            holdWriter.write(e.data).catch(() => { });
                        }
                    } catch (e) { }
                };

                const handleClose = (e) => {
                    try {
                        if (holdWriter) {
                            holdWriter.close().catch(() => { });
                            // transformStream.readable.cancel().catch(() => { });
                        }
                    } catch (e) { }
                };

                const handleError = (e) => {
                    try {
                        if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                            tcpInterface.close();
                        }
                        if (server && server.close instanceof Function) {
                            server.close(1013);
                        }
                        if (holdWriter) {
                            holdWriter.abort().catch(() => { });
                            // transformStream.readable.cancel().catch(() => { });
                        }
                    } catch (e) { }
                };

                // server.addEventListener("message", handleMessage);
                // server.addEventListener("close", handleClose);
                // server.addEventListener("error", handleError);

                server['onmessage'] = handleMessage;
                server['onclose'] = handleClose;
                server['onerror'] = handleError;

                return transformStream.readable;
            })(
                new TransformStream()
            )

        } else if (ingressMode === "readable") {
            /* ----- ReadableStream implementation ----- */
            upstreamReadable = new ReadableStream({
                start(controller) {

                    if (earlyHeader) {
                        try {
                            const earlyData = decodeBase64Url(earlyHeader);
                            if (earlyData) {
                                controller.enqueue(earlyData);
                            }
                        } catch (e) { }
                    }

                    const handleMessage = (e) => {
                        try {
                            controller.enqueue(e.data);
                        } catch (e) { }
                    };

                    const handleClose = (e) => {
                        try {
                            controller.close();
                        } catch (e) { }
                    };

                    const handleError = (e) => {
                        try {
                            if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                                tcpInterface.close();
                            }
                            if (server && server.close instanceof Function) {
                                server.close(1013);
                            }
                            controller.error(e);
                        } catch (e) { }
                    };

                    // server.addEventListener("message", handleMessage);
                    // server.addEventListener("close", handleClose);
                    // server.addEventListener("error", handleError);

                    server['onmessage'] = handleMessage;
                    server['onclose'] = handleClose;
                    server['onerror'] = handleError;
                },
            });
        }

        upstreamReadable.pipeTo(
            new WritableStream({
                async write(chunk, controller) {
                    try {
                        if (tcpWriter) {
                            if (tcpWriter.desiredSize === null || tcpWriter.desiredSize === 0) {
                                // console.log(`Chunks desiredSize: ${tcpWriter.desiredSize}`);
                                console.log(`This WritableStream has been closed, skipping write.`);
                                // throw new Error(`WritableStream has been closed`);
                                // return Promise.reject(new Error(`WritableStream has been closed`));
                                controller.error(`WritableStream has been closed`);
                            }

                            try {
                                // await tcpWriter.ready;
                                await tcpWriter.write(chunk);
                                return;
                            } catch (error) {
                                console.error('Write failed:', error);
                                // throw error;
                                // return Promise.reject(error);
                                controller.error(error);
                            }
                        }

                        const header = parseProtocolHeader(chunk, protocolMode, server);

                        try {
                            tcpInterface = await createConnection(header, globalControllerConfig.connectMode, protocolMode);
                            await tcpInterface.opened;
                        } catch (connectError) {
                            console.warn('First connection failed, retrying with relay mode');
                            tcpInterface = await createConnection(header, globalControllerConfig.retryMode, protocolMode);
                            await tcpInterface.opened;
                        }

                        tcpWriter = tcpInterface.writable.getWriter();

                        if (protocolMode === globalControllerConfig.targetProtocolType0) {
                            // server.send(new Uint8Array([header.version, 0]));
                            // server.send(Uint8Array.from([header.version, 0]));
                            server.send(Uint8Array.of(header.version, 0));

                            // server.send(new Uint8Array([header.version, 0]).buffer);
                            // server.send(Uint8Array.from([header.version, 0]).buffer);
                            // server.send(Uint8Array.of(header.version, 0).buffer);
                        }

                        if (header.rawClientData) {
                            await tcpWriter.write(header.rawClientData);
                        }

                        (async () => {
                            tcpReader = tcpInterface.readable.getReader();
                            try {
                                while (true) {
                                    const { value, done } = await tcpReader.read();
                                    if (done) break;
                                    if (!value || value.byteLength === 0) continue;

                                    if (server.readyState === WS_STATES.OPEN) {
                                        server.send(value);
                                    }
                                }
                            } catch (error) {
                                GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
                            } finally {
                                // try { tcpReader.cancel(); } catch { }
                                try { tcpReader.releaseLock(); } catch { }
                            }
                        })().catch((error) => {
                            GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
                        });

                    } catch (error) {
                        GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
                        throw error;
                    }
                },
                close(controller) { },
                abort(reason) { }
            })
        ).catch(e => {
            try {
                if (server && server.close instanceof Function) {
                    server.close(1013, e.message);
                }
            } catch { }
            try {
                if (tcpInterface && tcpInterface.close instanceof Function) {
                    tcpInterface.close();
                }
            } catch { }
        });

        return new Response(null, { status: 101, webSocket: client });
    } catch (error) {
        GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
        return new Response('Session failed', { status: 500 });
    }
}

async function createConnection(header, mode, protocolMode) {
    const { addressType, addressRemote, portRemote } = header;
    const useTargetProtocol = protocolMode === globalControllerConfig.targetProtocolType0;

    switch (mode) {
        case 'relayip': {
            const needDirect =
                [1].includes(addressType) ||
                (useTargetProtocol && [3].includes(addressType)) ||
                (!useTargetProtocol && [4].includes(addressType));
            return needDirect
                ? connect({ hostname: addressRemote, port: portRemote })
                : connect({
                    hostname: globalSessionConfig.relay.ip,
                    port: globalSessionConfig.relay.port || portRemote,
                });
        }
        case 'relaysocks': {
            // return socks5Connect(addressType, addressRemote, portRemote, protocolMode);
            return await socks5Connect(addressType, addressRemote, portRemote, protocolMode);
        }
        case 'direct': {
            return connect({ hostname: addressRemote, port: portRemote });
        }
        default:
            return connect({ hostname: addressRemote, port: portRemote });
    }
}

function matchUuid(extractedID, uuidString) {
    uuidString = uuidString.replaceAll('-', '')
    for (let index = 0; index < 16; index++) {
        const expected = parseInt(uuidString.substring(index * 2, index * 2 + 2), 16)
        if (extractedID[index] !== expected) {
            return false
        }
    }
    return true
}

// 🔧 完整的协议头解析函数 (VLESS/Trojan) - 使用 offset 优化
function parseProtocolHeader(buffer, protocolMode, wsInterface = null) {
    if (!buffer || buffer.byteLength === 0) throw new Error('Invalid buffer');

    const bytes = new Uint8Array(buffer);
    const view = new DataView(buffer);
    const decoder = new TextDecoder();

    if (protocolMode === globalControllerConfig.targetProtocolType0) {
        // VLESS 协议解析
        if (buffer.byteLength < 24) {
            throw new Error('Invalid VLESS buffer');
        }

        // UUID 验证 (需要 Uint8Array)
        const extractedID = bytes.subarray(1, 17);
        if (!matchUuid(extractedID, globalSessionConfig.user.id)) {
            if (wsInterface && wsInterface.close instanceof Function) {
                wsInterface.close(1013, 'Invalid user');
            }
            throw new Error('Invalid user: UUID does not match');
        }

        const version = view.getUint8(0);
        const optLength = view.getUint8(17);
        const commandIndex = 18 + optLength;
        const command = view.getUint8(commandIndex); // 0x01 TCP, 0x02 UDP, 0x03 MUX
        const portIndex = 18 + optLength + 1;
        const port = view.getUint16(portIndex); // port is big-Endian in raw data etc 80 == 0x005d

        let addressIndex = portIndex + 2;
        const addressType = view.getUint8(addressIndex);
        let hostname = '';
        let offset = addressIndex + 1; // 从 addressType 之后开始使用 offset

        // 地址解析
        switch (addressType) {
            case 1: // IPv4
                if (offset + 4 > bytes.length) throw new Error('Invalid IPv4 address');
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            case 2: // Domain name
                if (offset >= bytes.length) throw new Error('Invalid domain length');
                const domainLength = bytes[offset++];
                if (offset + domainLength > bytes.length) throw new Error('Invalid domain');
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLength));
                offset += domainLength;
                break;
            case 3: // IPv6
                if (offset + 16 > bytes.length) throw new Error('Invalid IPv6 address');
                const ipv6Parts = [];
                for (let i = 0; i < 16; i += 2) {
                    ipv6Parts.push(((bytes[offset + i] << 8) | bytes[offset + i + 1]).toString(16));
                }
                hostname = ipv6Parts.join(':');
                offset += 16;
                break;
            default:
                throw new Error(`Unsupported address type: ${addressType}`);
        }

        const rawClientData = bytes.subarray(offset);

        return {
            addressType,
            addressRemote: hostname,
            portRemote: port,
            rawClientData,
            version
        };
    } else if (protocolMode === globalControllerConfig.targetProtocolType1) {
        // Trojan 协议解析
        if (buffer.byteLength < 60) {
            throw new Error('Invalid Trojan buffer');
        }

        const crLfIndex = 56;
        const extractedPassword = decoder.decode(bytes.subarray(0, crLfIndex));
        if (extractedPassword !== globalSessionConfig.user.sha224) {
            if (wsInterface && wsInterface.close instanceof Function) {
                wsInterface.close(1013, 'Invalid password');
            }
            throw new Error('Invalid password');
        }

        const command = view.getUint8(crLfIndex + 2);
        const addressType = view.getUint8(crLfIndex + 3);
        let hostname = '';
        let addressIndex = crLfIndex + 4;
        let offset = addressIndex; // 从 addressType 之后开始使用 offset

        // 地址解析 (Trojan 使用不同的地址类型映射)
        switch (addressType) {
            case 1: // IPv4
                if (offset + 4 > bytes.length) throw new Error('Invalid IPv4 address');
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            case 3: // Domain name (Trojan 使用 3)
                if (offset >= bytes.length) throw new Error('Invalid domain length');
                const domainLength = bytes[offset++];
                if (offset + domainLength > bytes.length) throw new Error('Invalid domain');
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLength));
                offset += domainLength;
                break;
            case 4: // IPv6 (Trojan 使用 4)
                if (offset + 16 > bytes.length) throw new Error('Invalid IPv6 address');
                const ipv6Parts = [];
                for (let i = 0; i < 16; i += 2) {
                    ipv6Parts.push(((bytes[offset + i] << 8) | bytes[offset + i + 1]).toString(16));
                }
                hostname = ipv6Parts.join(':');
                offset += 16;
                break;
            default:
                throw new Error(`Unsupported address type: ${addressType}`);
        }

        const port = view.getUint16(offset);
        offset += 2;
        offset += 2; // 跳过 CRLF
        const rawClientData = bytes.subarray(offset);

        return {
            addressType,
            addressRemote: hostname,
            portRemote: port,
            rawClientData
        };
    } else {
        throw new Error(`Unsupported protocol mode: ${protocolMode}`);
    }
}

function decodeBase64Url(encodedString) {
    return Uint8Array.from(atob(encodedString.replaceAll('-', '+').replaceAll('_', '/')), (c) => c.charCodeAt(0)).buffer;
}

async function socks5Connect(addressType, addressRemote, portRemote, protocolMode) {
    const { username, password, hostname, port } = socks5AddressParser(globalSessionConfig.relay.socks);
    let socket, reader, writer;
    const encoder = new TextEncoder()
    try {
        // Connect to the SOCKS server
        socket = connect({ hostname, port });
        reader = socket.readable.getReader();
        writer = socket.writable.getWriter();
        if (!reader || !writer) throw new Error(`reader or writer is null`);

        // Send SOCKS5 greeting
        const socksGreeting = new Uint8Array([5, 2, 0, 2]); // Support No Auth and Username/Password Auth
        await writer.write(socksGreeting);

        // Read server response
        let res = (await reader.read()).value;
        if (res[0] !== 0x05) throw new Error(`Invalid SOCKS5 response: ${res[0]}`);
        if (res[1] === 0xff) throw new Error(`SOCKS5 authentication rejected`);

        // Handle authentication if required
        if (res[1] === 0x02) {
            if (!username || !password) throw new Error(`SOCKS5 auth required`);
            const authRequest = new Uint8Array([
                1,
                username.length,
                ...encoder.encode(username),
                password.length,
                ...encoder.encode(password)
            ]);
            await writer.write(authRequest);
            res = (await reader.read()).value;
            if (res[0] !== 0x01 || res[1] !== 0x00) throw new Error(`SOCKS5 authentication failed`);
        }

        // Prepare and send request
        let DSTADDR;
        const addressTypeMap = protocolMode === globalControllerConfig.targetProtocolType0
            ? { IPv4: 1, DOMAIN: 2, IPv6: 3 }
            : { IPv4: 1, DOMAIN: 3, IPv6: 4 };
        switch (addressType) {
            case addressTypeMap.IPv4:
                DSTADDR = new Uint8Array([1, ...addressRemote.split('.').map(Number)]);
                break;
            case addressTypeMap.DOMAIN:
                DSTADDR = new Uint8Array([3, addressRemote.length, ...encoder.encode(addressRemote)]);
                break;
            case addressTypeMap.IPv6:
                // DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => [parseInt(x.slice(0, 2), 16), parseInt(x.slice(2), 16)])]);
                // DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => x.padStart(4, '0').match(/../g).map(y => parseInt(y, 16)))]);
                DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => x.padStart(4, '0').match(/.{2}/g).map(y => parseInt(y, 16)))]);
                break;
            default:
                throw new Error(`Unsupported address type: ${addressType}`);
        }
        const socksRequest = new Uint8Array([5, 1, 0, ...DSTADDR, portRemote >> 8, portRemote & 0xff]);
        await writer.write(socksRequest);

        // Read final response
        res = (await reader.read()).value;
        if (res[1] !== 0x00) throw new Error(`SOCKS5 connection failed: ${res[1]}`);
        reader.releaseLock();
        writer.releaseLock();
        return socket;

    } catch (error) {
        if (reader) { reader?.releaseLock() }
        if (writer) { writer?.releaseLock() }
        if (socket) { socket.close(); socket = null; }
        console.log(`Error stack: ${error.message} stack: ${error.stack}`);
        throw new Error(`Connect socks5 error: ${error.message}`);
    }
}

function socks5AddressParser(address) {
    const [latter, former] = address.split("@").reverse();
    const [hostname, port] = latter.split(":");
    let username, password;
    if (former) {
        const formers = former.split(":");
        if (formers.length !== 2) {
            throw new Error('Invalid SOCKS address format: Expected "username:password" before "@"');
        }
        [username, password] = formers;
    }
    if (Object.is(port, NaN)) {
        throw new Error('Invalid SOCKS address format: Port must be a valid number');
    }
    return { username, password, hostname, port: Number(port) }
}

async function fetchRemoteData(url) {
    const randomVersion = Math.floor(Math.random() * (128 - 110 + 1)) + 110;

    const headers = new Headers({
        'User-Agent': `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${randomVersion}.0.0.0 Safari/537.36`,
    });

    const response = await fetch(url, { headers });
    if (!response.ok) {
        throw new Error('Failed to fetch the input string');
    }
    return response.text();
}

function encryptVar(str) {
    return str.split('').map(char => {
        return (char.charCodeAt(0) << 2).toString(16);
    }).join('-_-');
}

function decryptVar(str) {
    return str.split('-_-').map(hex => {
        return String.fromCharCode(parseInt(hex, 16) >> 2);
    }).join('');
}

const protocolTypes = {
    p0: decryptVar('1d8-_-1b0-_-194-_-1cc-_-1cc'),
    p1: decryptVar('1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8'),
};


function parseAddressLines(inputString) {
    const lines = inputString.trim().split('\n');
    const uniqueEntries = new Set(lines); // Use Set to remove duplicate lines
    const commentCounts = {};

    return Array.from(uniqueEntries).map(line => {
        let atValue, atType = null;
        let processedLine = line;

        // Extract type value if present
        const typeIndex = processedLine.indexOf('|');
        if (typeIndex > -1) {
            const typeValue = processedLine.substring(typeIndex + 1).trim().toLowerCase();
            atType = typeValue || null;
            processedLine = processedLine.slice(0, typeIndex).trim(); // Use the part before '|' for further processing
        }

        // Extract @ value if present
        const atIndex = processedLine.indexOf('@');
        if (atIndex > -1) {
            atValue = processedLine.substring(atIndex + 1).trim();
            processedLine = processedLine.substring(0, atIndex).trim(); // Use the part before '@' for further processing
        }

        const [addressPort, comment] = processedLine.split('#');
        let processedComment = comment ? comment.trim() : 'Unknown';

        commentCounts[processedComment] = (commentCounts[processedComment] || 0) + 1;
        processedComment += `-${commentCounts[processedComment]}`;

        if (addressPort.startsWith('[')) { // Handle IPv6 addresses
            const closingBracketIndex = addressPort.indexOf(']');
            if (closingBracketIndex > -1) {
                const address = addressPort.substring(0, closingBracketIndex + 1);
                const port = addressPort.slice(closingBracketIndex + 2).trim() || null;
                return { address, port: port || null, comment: processedComment, atValue, atType };
            }
        } else { // Handle IPv4 addresses or domain names
            const [address, port] = addressPort.trim().split(':'); // Trim to remove potential leading/trailing spaces
            return { address: address.trim(), port: port ? port.trim() : null, comment: processedComment, atValue, atType };
        }
    });
}

function generateConfigs(inputString, uuid, password, enableTls, domain) {
    const transportProtocol = 'ws';
    const fingerprint = 'chrome';

    // Get path based on protocol and additional parameters
    function getPath(protocol, atValue, atType) {
        const basePath = protocol === protocolTypes.p0
            ? '/?ed=2560'
            : '/trws?ed=2560';

        if (!atValue) return basePath;

        // Add parameters based on different types
        switch (atType?.toLowerCase()) {
            // case 'relayip':
            //     return `${basePath}&relayip=${atValue}`;
            case 'socks':
                return `${basePath}&socks=${atValue}`;
            default:
                return `${basePath}&relayip=${atValue}`;
        }
    }

    const parsedAddresses = parseAddressLines(inputString);
    const configs = {
        protocolP0: [],
        protocolP1: [],
        customP0: { fullConfig: [], namesOnly: [] },
        customP1: { fullConfig: [], namesOnly: [] }
        // customP0: { fullConfig: ['  - ' + JSON.stringify({ name: "Dns-out", type: "dns" })], namesOnly: [] },
        // customP1: { fullConfig: ['  - ' + JSON.stringify({ name: "Dns-out", type: "dns" })], namesOnly: [] }
    };

    // Generate protocol string with path parameters
    function generateProtocolString(protocol, credentials, address, port, comment, atValue, atType) {
        const securityType = enableTls ? 'tls' : 'none';
        const path = getPath(protocol, atValue, atType);
        const baseString = `${protocol}://${credentials}@${address}:${port}?security=${securityType}&sni=${domain}&fp=${fingerprint}&type=${transportProtocol}&host=${domain}&path=${encodeURIComponent(path)}`;
        const encryptionPart = protocol === protocolTypes.p0 ? '&encryption=none' : '';
        return `${baseString}${encryptionPart}#${encodeURIComponent(comment)}`;
    }

    // Generate custom config with path parameters
    function generateCustomConfig(protocol, address, port, comment, atValue, atType) {
        const isFirstProtocol = protocol === protocolTypes.p0;
        const nodeName = `${protocol}-${comment}`;
        const path = getPath(protocol, atValue, atType);

        return {
            name: nodeName,
            type: protocol,
            server: address,
            port: enableTls ? (port || 443) : (port || 80),
            [isFirstProtocol ? 'uuid' : 'password']: isFirstProtocol ? uuid : password,
            udp: true,
            tls: enableTls,
            network: "ws",
            [isFirstProtocol ? 'servername' : 'sni']: domain,
            ...(enableTls ? {
                "skip-cert-verify": false,
                "client-fingerprint": "chrome"
            } : {}),
            "ws-opts": {
                path: path,
                headers: {
                    Host: domain
                }
            }
        };
    }

    parsedAddresses.forEach(({ address, port, comment, atValue, atType }) => {
        const actualPort = enableTls ? (port || 443) : (port || 80);

        // Generate string configs
        const protocolP0 = generateProtocolString(protocolTypes.p0, uuid, address, actualPort, comment, atValue, atType);
        const protocolP1 = generateProtocolString(protocolTypes.p1, password, address, actualPort, comment, atValue, atType);

        configs.protocolP0.push(protocolP0.trim());
        configs.protocolP1.push(protocolP1.trim());

        // Generate custom configs
        const customP0 = generateCustomConfig(protocolTypes.p0, address, actualPort, comment, atValue, atType);
        const customP1 = generateCustomConfig(protocolTypes.p1, address, actualPort, comment, atValue, atType);

        configs.customP0.fullConfig.push('  - ' + JSON.stringify(customP0));
        configs.customP1.fullConfig.push('  - ' + JSON.stringify(customP1));
        configs.customP0.namesOnly.push(`      - "${customP0.name}"`);
        configs.customP1.namesOnly.push(`      - "${customP1.name}"`);
    });

    return {
        protocolP0: configs.protocolP0.join('\n'),
        protocolP1: configs.protocolP1.join('\n'),
        customP0: {
            fullConfig: configs.customP0.fullConfig.join('\n'),
            namesOnly: configs.customP0.namesOnly.join('\n')
        },
        customP1: {
            fullConfig: configs.customP1.fullConfig.join('\n'),
            namesOnly: configs.customP1.namesOnly.join('\n')
        }
    };
}

function getCustomConfig(inputString, uuid, pass, tls, domain, configType, inputTemplate) {
    const configs = generateConfigs(inputString, uuid, pass, tls, domain);
    let proxiesConfig, namesOnly;

    switch (configType.toLowerCase()) {
        case 'p0':
            proxiesConfig = configs.customP0.fullConfig;
            namesOnly = configs.customP0.namesOnly;
            break;
        case 'p1':
            proxiesConfig = configs.customP1.fullConfig;
            namesOnly = configs.customP1.namesOnly;
            break;
        case 'both':
            proxiesConfig = configs.customP0.fullConfig + '\n' + configs.customP1.fullConfig;
            namesOnly = configs.customP0.namesOnly + '\n' + configs.customP1.namesOnly;
            break;
        default:
            throw new Error(`Invalid configType: ${configType}. Supported types are 'p0', 'p1', and 'both'.`);
    }

    inputTemplate = inputTemplate.replace('${proxiesConfig}', proxiesConfig);
    return inputTemplate;
}